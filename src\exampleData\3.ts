import type { MindElixirData } from '../index'

const frontEndSkill: MindElixirData = {
  nodeData: {
    topic: 'web前端能力地图',
    id: 'me-root',
    children: [
      {
        topic: '专业能力',
        id: '31674e797740932bd85937f60a271ce9',
        children: [
          {
            topic: '初阶',
            id: '8aac6fd006f93acc92108dde88bbb371',
            children: [
              {
                topic: '前端基础',
                id: '878fbc7deac7d928fce359900cf81fef',
                children: [
                  {
                    topic: '客户端',
                    id: 'da26d49270305392815cea3f8b6f0796',
                    children: [
                      {
                        topic: '浏览器',
                        id: 'eec82aa0f673b7ffc6ea68fe24078309',
                        children: [
                          {
                            topic: 'Chrome',
                            id: '660f7cd083cef15a0a38c74cc9fde4f2',
                          },
                          {
                            topic: '微信内置浏览器',
                            id: 'e6cfa2b46b842d3899c08d73ed0b4a43',
                          },
                        ],
                      },
                      {
                        topic: 'APP',
                        id: '3f472c19c6dff87242e5c6aacaa7afd2',
                        children: [
                          {
                            topic: '小程序',
                            id: '7abc617cff7a076041b8d38362c9a67c',
                          },
                          {
                            topic: 'WebView',
                            id: 'bbbfba5924bd3f0c55bb37c8081d4ebd',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '网络',
                    id: '2fa503c93c56ab4b8a764df08cc57abb',
                    children: [
                      {
                        topic: 'Domain',
                        id: '50cf1b9c278606cd47a5a3817adc638a',
                      },
                      {
                        topic: 'TCP/IP',
                        id: 'e8e7bc4206eabc59624e17498f58249c',
                      },
                      {
                        topic: 'DNS',
                        id: '595c0850a64785b537287eb2b468eec4',
                      },
                      {
                        topic: 'HTTP',
                        id: '2aef90d1b0fd75713b08e2df3ff28f46',
                      },
                    ],
                  },
                  {
                    topic: '服务端',
                    id: 'ee6b09e9ce7be6f2742d3ffccc65a1d4',
                    children: [
                      {
                        topic: 'WebServer',
                        id: 'bc557882b7c313b1980cbe680a14c985',
                        children: [
                          {
                            topic: 'Nginx',
                            id: 'f3feafa53304e8a3143ed0e9f3e1fac9',
                          },
                          {
                            topic: 'Apache HTTP Server',
                            id: '0ad21d131b8a8283f63e7a4ebf8defe1',
                          },
                          {
                            topic: 'CDN',
                            id: 'af0bdd4b91061ebedb4a523f918bd47e',
                          },
                        ],
                      },
                      {
                        topic: '服务端语言',
                        id: 'f1fa46104b01102db269d1909d208ff7',
                        children: [
                          {
                            topic: 'Java',
                            id: 'd9b2627ab5875b4c7b1b17d0a2e4b010',
                          },
                          {
                            topic: 'Golang',
                            id: 'bc12d81e717be8059be2394788440137',
                          },
                          {
                            topic: 'Python',
                            id: '78831f7ccaa673617adefb88250dd9de',
                          },
                        ],
                      },
                      {
                        topic: '数据库',
                        id: 'b8621ccd3a037349098125ebb5ed932e',
                        children: [
                          {
                            topic: 'MySQL',
                            id: '7f4e8c09c1f19698449cb1ec8f4fea4a',
                          },
                          {
                            topic: 'MongoDB',
                            id: '16710cea5cfb50712c8832676b87d2cc',
                          },
                        ],
                      },
                      {
                        topic: '操作系统',
                        id: 'a3d072a89b06377d3e27937fee021cff',
                        children: [
                          {
                            topic: 'Linux',
                            id: 'e72c34d6886dd4dcecf2d5df342f3c4e',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '行业标准',
                    id: '6595463e8a33b2faf3d13bff312bfb5f',
                    children: [
                      {
                        topic: 'W3C/WHATWG',
                        id: 'e941274d59b2a3f6fd48f15fefeb30e7',
                        children: [
                          {
                            topic: 'CSS标准',
                            id: 'ad84cd730a702498f54b040c050c5d32',
                          },
                          {
                            topic: 'HTML、XHTML、XML、HTML5标准',
                            id: '79dc813fb90805d518946c26424cfeb1',
                          },
                          {
                            topic: 'DOM标准',
                            id: 'e3a3d011765a4d11a9dd73f9b4d540d4',
                          },
                          {
                            topic: 'SVG标准',
                            id: '6b0774c945df07be86f79fb5b00bdd32',
                          },
                          {
                            topic: '小程序标准',
                            id: '70cfcbedebae12ebd92cd2555e8d04b4',
                          },
                        ],
                      },
                      {
                        topic: 'ECMA-TC39',
                        id: '5c7bdc4d497f1f28ed6644a4dd2a9910',
                        children: [
                          {
                            topic: 'EcmaScript标准',
                            id: '22a562e41becc77095c039092c4d92b7',
                          },
                          {
                            topic: 'JavaScript标准',
                            id: 'abf39a44037669f5980a6ce807e2af72',
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                topic: '研发工具',
                id: 'a9d4061ae43393fbd00cdd19e7606215',
                children: [
                  {
                    topic: '编辑器',
                    id: '279efaf9e60aac9bbf99ba03cdbc2326',
                    children: [
                      {
                        topic: 'Visual Studio Code',
                        id: 'cb07f954f6deeac8cdc8b7332e852592',
                      },
                      {
                        topic: 'WebStorm',
                        id: '25314a128c82e73a9a77b013477c5de1',
                      },
                      {
                        topic: 'Sublime Text',
                        id: '9e1f252caaea4b414890f03594f5aefd',
                      },
                    ],
                  },
                  {
                    topic: '调试预览',
                    id: 'ac6c91e905bc11c3c99b92188d0d7e33',
                    children: [
                      {
                        topic: '浏览器调试工具',
                        id: '1ce51746dc86d04c1ea313d1db68a15d',
                        children: [
                          {
                            topic: 'Chrome DevTools',
                            id: '72e3db2365908f96c51b3e4857060576',
                          },
                          {
                            topic: '微信开发者工具',
                            id: '9ea4f48e0c5382940ffa388a68281e74',
                          },
                          {
                            topic: 'vConsole',
                            id: '763e15cd76798903155c3965d961cec8',
                          },
                        ],
                      },
                      {
                        topic: '本地服务',
                        id: '75fceaf637f8fad547e183e3f8ab1472',
                        children: [
                          {
                            topic: 'file://',
                            id: 'c4ff4bd9c4c0997de5f100c90d0d89a8',
                          },
                          {
                            topic: 'http://、SimpleHTTPServer',
                            id: 'ed1698058fd5ae49d36d518d3a80a115',
                          },
                        ],
                      },
                      {
                        topic: '在线服务',
                        id: '93e195bb7cdf12f666b239bfb7f72ab7',
                        children: [
                          {
                            topic: 'CodePen',
                            id: 'f91d2158ce4546b64b1b07d70f0856f3',
                          },
                          {
                            topic: 'JSFiddle',
                            id: '5afe735ffa45d26c8470d0ab385f2f07',
                          },
                          {
                            topic: 'github1s',
                            id: '8d6dec6609322cc20c4fec74c188319c',
                          },
                        ],
                      },
                      {
                        topic: '网络调试',
                        id: 'cac051fea196400dd0d43f0a4cd568f3',
                        children: [
                          {
                            topic: 'hosts、Switchhosts',
                            id: '4eb2b342b8ed94d34ca3d56c4ac80851',
                          },
                          {
                            topic: 'Debugging Proxy、Charles、wireshark',
                            id: '750ea29ab40d3928c73fe49d5591b698',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '图片编辑器',
                    id: '0f2f00568333c2408bbb7886db69d2ca',
                    children: [
                      {
                        topic: 'Photoshop',
                        id: 'f4464dea35369505b68446c56f7ddfd6',
                      },
                      {
                        topic: 'Sketch',
                        id: 'd2dad47f7ff75750a540daf7efe60b23',
                      },
                      {
                        topic: '蓝湖',
                        id: '1f368c8d993b89416fc78ce437bbe746',
                      },
                      {
                        topic: '摹客',
                        id: 'd18a47cd36ebad761d8d0464207c39d7',
                      },
                      {
                        topic: 'figma',
                        id: '473bcb154547a41a002718b7baf8a413',
                      },
                    ],
                  },
                  {
                    topic: '版本管理',
                    id: 'bfe3e81cd4d8ca5258150247e7991171',
                    children: [
                      {
                        topic: 'git',
                        id: 'bf06e9d6ce20a6e39f33fc316ef59d2c',
                      },
                      {
                        topic: 'SVN',
                        id: '68eee02d166735e798a544960b5e6f61',
                      },
                    ],
                  },
                ],
              },
              {
                topic: 'HTML',
                id: '224d1a9501485d063e295e4734a929f2',
                children: [
                  {
                    topic: '元素',
                    id: '9bd8a0a2194fb18b6156e3777921c47f',
                    children: [
                      {
                        topic: '根元素',
                        id: '11b56bf6f8a6eaf94c1041b86bd9f56f',
                        children: [
                          {
                            topic: '主根元素html',
                            id: '35d5af054234d66be64d4051ec7782e7',
                          },
                          {
                            topic: '分区根元素body',
                            id: '42bde3da38a0a5b860155dd1e89cec7b',
                          },
                        ],
                      },
                      {
                        topic: '元数据',
                        id: '8c5be907330aecb85e1739b6f213d432',
                        children: [
                          {
                            topic: 'base',
                            id: 'be420b6f068eaf12e60f46b1e8d99636',
                          },
                          {
                            topic: 'head',
                            id: '814836dde3998eb919a01a82c8de81a6',
                          },
                        ],
                      },
                      {
                        topic: '内容分区',
                        id: '20d975e504ae04130beb374dff1a10fd',
                        children: [
                          {
                            topic: 'header',
                            id: '651ebebfd8064edd02b7eaddb6877db3',
                          },
                          {
                            topic: 'footer',
                            id: '1482d802c366557aa742d7618955d454',
                          },
                        ],
                      },
                      {
                        topic: '内容',
                        id: '982c2e623f7807fbcf22f11b7b3f3da4',
                        children: [
                          {
                            topic: '块级内容div、dir',
                            id: '551f3a84c00fb6f555aeff605fc8deb8',
                          },
                          {
                            topic: '文字内容a、b、strong',
                            id: '7471517921b78db7ef2a01905361e35b',
                          },
                        ],
                      },
                      {
                        topic: '图片和多媒体',
                        id: '9a1f340cf9fedf52ceaf9b91396011ee',
                        children: [
                          {
                            topic: 'audio',
                            id: '7bf41be43ffa20b5eee603bde38f3848',
                          },
                          {
                            topic: 'img',
                            id: 'd4ec18739d6ce88a85bbb7d6559cc48e',
                          },
                        ],
                      },
                      {
                        topic: '内嵌内容',
                        id: 'dd0379bc81586da5c83a617e3e909953',
                        children: [
                          {
                            topic: 'iframe',
                            id: '0d20c30697c4f8dbda8e99669c1c6123',
                          },
                          {
                            topic: 'object',
                            id: '650f22160a8e58109fc0387fb2264bc1',
                          },
                        ],
                      },
                      {
                        topic: '脚本',
                        id: '4ddde8fbe2161af6deacc013fda0f687',
                        children: [
                          {
                            topic: 'canvas',
                            id: 'e258a6e0d0c7d22053519d254287c1ff',
                          },
                          {
                            topic: 'script',
                            id: '8dc66fc7802dfa72b395ab1a2dc468c9',
                          },
                        ],
                      },
                      {
                        topic: '表格',
                        id: 'fa8a188b2ce2774d899439c2250c0d3d',
                        children: [
                          {
                            topic: 'table',
                            id: 'b844737e83330c6eae1aaa33e9880fa8',
                          },
                          {
                            topic: 'tbody',
                            id: 'ac9f26165d7111a5803e5a624416af3e',
                          },
                        ],
                      },
                      {
                        topic: '表单',
                        id: 'b40fd0c94f641907e8748f586775eec4',
                        children: [
                          {
                            topic: 'button',
                            id: '87aa4bbf4d73a0dab525a852a6b3a2b7',
                          },
                          {
                            topic: 'input',
                            id: '1acb0ac6530120202106faf8d7dc8c04',
                          },
                        ],
                      },
                      {
                        topic: '可交互元素',
                        id: '5d0210b9bc5bbdd780d8886e78acd4f8',
                        children: [
                          {
                            topic: 'menu',
                            id: 'a217787ad9659b6aa290995c9cb70996',
                          },
                          {
                            topic: 'menuitem',
                            id: '2db6ffcc9338a5b458e51e79d5e9275d',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '属性',
                    id: '7c974d57cf5176ad7e8724be4e3c8aa2',
                    children: [
                      {
                        topic: '常用属性',
                        id: '2a19af9139885de6c257ee0a27bcc4f6',
                        children: [
                          {
                            topic: 'class',
                            id: '1a4c07c5e74cea4ccdbe829d5b2ff430',
                          },
                          {
                            topic: 'id',
                            id: 'd5d612cbdeecbca87c8527557927efce',
                          },
                          {
                            topic: 'style',
                            id: '02b9d58b26e2fb97f2477e23e4840917',
                          },
                          {
                            topic: 'title',
                            id: 'f40981a62f1ce5351c02c5731a8535e8',
                          },
                        ],
                      },
                      {
                        topic: '全局属性',
                        id: '7e94765996dc13e45dadf93196e49eef',
                      },
                    ],
                  },
                  {
                    topic: '事件',
                    id: 'e82e8caa7030a2e2799f945c5033ba06',
                    children: [
                      {
                        topic: '窗口事件',
                        id: '657f82ec314496b90e2528ddf48ded73',
                      },
                      {
                        topic: '表单事件',
                        id: '954e38edebb2772a26ce90a07a179ec3',
                      },
                      {
                        topic: '键盘事件',
                        id: 'd570b0f6fa26d87479e80fed7202197b',
                      },
                      {
                        topic: '鼠标事件',
                        id: '1e968f11315d5090ae5f6cf3e90f83bd',
                      },
                      {
                        topic: '多媒体事件',
                        id: '2c7dbadfc7840b430cb53f7eb3ce6989',
                      },
                    ],
                  },
                  {
                    topic: '编码',
                    id: '206b18c2aec8fa440f6c9a1c43084b5d',
                    children: [
                      {
                        topic: 'URL编码',
                        id: '46a92523cd4a3eca0dcb6fc6654aaf35',
                      },
                      {
                        topic: '语言编码',
                        id: '848c2aa695564789bc14171324c74164',
                      },
                      {
                        topic: '字符集',
                        id: '5f250e4d0e8e7aaf6f05ebb93cf930c3',
                      },
                    ],
                  },
                ],
              },
              {
                topic: 'CSS',
                id: '2738390f5a5b5a2592e289251e153eae',
                children: [
                  {
                    topic: '语法',
                    id: '955e835f979dbe12402d464aae79df19',
                    children: [
                      {
                        topic: '@规则',
                        id: '4d533b76c0b659132a87dd7a20f2348e',
                      },
                      {
                        topic: '层叠',
                        id: '9c29d90fb30f9c36393a5f01192d61c0',
                      },
                      {
                        topic: '注释',
                        id: '9ce4b1f6aba05533ffd7fa1e85541aee',
                      },
                      {
                        topic: '解释器',
                        id: '276f1c16a7f3cfd1b1498ad247b53bfe',
                      },
                      {
                        topic: '继承',
                        id: '6f47acb14c875668f5531b8c96ada1e2',
                      },
                      {
                        topic: '简写',
                        id: 'f91c0e7332471d6ffdd3c5bf845f423c',
                      },
                      {
                        topic: '优先级',
                        id: '2be9c9398583519e3212c4258acf8f32',
                      },
                      {
                        topic: '值定义',
                        id: '0a37a9481ef4cb110d73ae82dc098ad5',
                      },
                      {
                        topic: '单位与取值类型',
                        id: '96a6f5df2b0c7a426b14dc9ed1fb2574',
                      },
                    ],
                  },
                  {
                    topic: '选择器',
                    id: '75130ee37d2046a76ede404e5150569e',
                    children: [
                      {
                        topic: '元素选择器',
                        id: '3241f2e67169ddef34521e2be2d608fe',
                      },
                      {
                        topic: '选择器分组',
                        id: '800f1843d3347ef2028baa13bd40994b',
                      },
                      {
                        topic: '类选择器',
                        id: '5eaa3f46592944d4298dfa16e1a48d06',
                      },
                      {
                        topic: 'ID选择器',
                        id: '138492d495d99381e42535416cbadd5f',
                      },
                      {
                        topic: '属性选择器',
                        id: '3e1ecdeef855c5d04f0c693d1576541f',
                      },
                      {
                        topic: '后代选择器',
                        id: '5627038331a7c9130057f20b09fb9313',
                      },
                      {
                        topic: '子元素选择器',
                        id: '477eb83f5facb7b6ca62d3a07d4aeb55',
                      },
                      {
                        topic: '相邻选择器',
                        id: 'b3b30c773d035bdaad436c66ad1d6fbf',
                      },
                      {
                        topic: '伪类',
                        id: 'a87eeff261316d81f9d83661bb08af9c',
                      },
                      {
                        topic: '伪元素',
                        id: '430c8aa651b788d205f7b8eca0c3a04e',
                      },
                    ],
                  },
                  {
                    topic: '定位',
                    id: 'cc6ad1bac45557a7a6636609b34ff59a',
                    children: [
                      {
                        topic: 'position',
                        id: '571b56b1dfb52ad8eb37a08741f99a90',
                      },
                      {
                        topic: 'top、left、bottom、right',
                        id: '639d68d10450fcac5115e2a057a2f352',
                      },
                      {
                        topic: 'z-index',
                        id: 'a932ae00fd65919839b5d4ed5d82285e',
                      },
                    ],
                  },
                  {
                    topic: '布局',
                    id: '2415b6e6af4966a4ed0f12e80a4b3877',
                    children: [
                      {
                        topic: 'Box Model',
                        id: '********************************',
                      },
                      {
                        topic: 'FlexBox',
                        id: '********************************',
                      },
                      {
                        topic: 'Grid',
                        id: 'bfcff8726f64fd823336ab22835a678c',
                      },
                      {
                        topic: 'Column',
                        id: 'e72e8b3297a7587eda12cb357857ddb0',
                      },
                    ],
                  },
                  {
                    topic: '样式',
                    id: 'f39148af0020e26960a3a9978b7502b0',
                    children: [
                      {
                        topic: '背景',
                        id: '50703eafb8d64142584d6fe070760626',
                      },
                      {
                        topic: '文本',
                        id: 'eeba72ca38825bb15109462afd3f625c',
                      },
                      {
                        topic: '轮廓',
                        id: 'ca27e160020891310cc7a5be616f651c',
                      },
                      {
                        topic: '列表',
                        id: '88a829ae37794d2653f306616a59b98e',
                      },
                    ],
                  },
                  {
                    topic: '动画',
                    id: '9f2a9bbc90eb6a2d13d48ac6aa6d791b',
                    children: [
                      {
                        topic: 'Animation',
                        id: '910a1ab848cb2f50524b56366d5834e9',
                      },
                      {
                        topic: 'transition',
                        id: '1c47b503c9b774acaac991a1ed977f77',
                      },
                    ],
                  },
                  {
                    topic: '应用',
                    id: 'c7566490b49b837e1b9df20d8151006d',
                    children: [
                      {
                        topic: '响应式',
                        id: '737f2adb0d9f32741ad2dc3394451822',
                        children: [
                          {
                            topic: 'em',
                            id: '0560b062a18ac7ad28435807c8959242',
                          },
                          {
                            topic: 'vh/vw',
                            id: 'ae98d378740b9b6289114c6d752bd30e',
                          },
                          {
                            topic: '%',
                            id: '841ff3b9a82a41b78ff884669b091ba8',
                          },
                        ],
                      },
                      {
                        topic: '自适应',
                        id: '3a8b7f0eda3acb895a21464a89376835',
                        children: [
                          {
                            topic: '@media',
                            id: '34bacf7b89b3b38a904a210f904a85c3',
                          },
                          {
                            topic: 'rem',
                            id: 'a18a86af33a57b198125acbcaa9b6e02',
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                topic: 'JavaScript',
                id: '99557e3b804ca1ec5f184e8ba13f2134',
                children: [
                  {
                    topic: '语法',
                    id: '9d9492cfa118aad0b086f925b591e565',
                    children: [
                      {
                        topic: '值、变量',
                        id: 'b6fbf7a702394a59b23257fa5e69f6d6',
                      },
                      {
                        topic: '数据类型',
                        id: '6c7867ace5b6415619790352987ef43b',
                        children: [
                          {
                            topic: '基本类型',
                            id: 'b2dddd77d4a130ed46165e0281fff8a1',
                          },
                          {
                            topic: '类型判断',
                            id: 'e180f01b186d26e6acd9f00ea39bfd81',
                          },
                          {
                            topic: '类型转换',
                            id: '2f4f77b47893f477f46513ebe6bf39f0',
                          },
                        ],
                      },
                      {
                        topic: '流程控制',
                        id: '2b4e5a5192cb038082c89e6c87cf36c5',
                      },
                      {
                        topic: '运算（表达式、运算符）',
                        id: '8c94d19915fe55e5ac9fe43470780974',
                        children: [
                          {
                            topic: '数学',
                            id: 'f5d1082326f3c4f9b05188477b63d4b7',
                          },
                          {
                            topic: '比较',
                            id: '20c521d6aaae55d571a869876fad041b',
                          },
                          {
                            topic: '逻辑',
                            id: 'dc4a8011779f5708c172d42d43447abf',
                          },
                        ],
                      },
                      {
                        topic: '函数',
                        id: 'ae9581f1332c372db5fb37f83c70e95d',
                        children: [
                          {
                            topic: '函数声明',
                            id: 'f9a1027b72d7c50daaa6cb666f6dfe8e',
                          },
                          {
                            topic: '函数表达式',
                            id: '963a50442a603514111581d9dd2d9eb2',
                          },
                          {
                            topic: '回调函数',
                            id: 'acc027399883761e276b6e0befbfda8d',
                          },
                          {
                            topic: '箭头函数',
                            id: 'af9e8393844ae895aee9a31bcd863c87',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '数据类型深入',
                    id: '14a7654de6e2a9f58e20f7df18263c30',
                    children: [
                      {
                        topic: '原始类型的方法',
                        id: 'ef562f2d5f09d1f873224732c7cb25bf',
                      },
                      {
                        topic: '数字类型',
                        id: 'ac16fa44442bdc817ad99fa116c9fd99',
                      },
                      {
                        topic: '字符串',
                        id: '46097f6242a03e3a38493aedba106ec2',
                      },
                      {
                        topic: '数组',
                        id: '131d8286c9e4626c7564b81abcc6c3fb',
                      },
                      {
                        topic: '数组方法',
                        id: '13a2985a38e9e16b23beeac6231b4043',
                      },
                      {
                        topic: 'Iterable object（可迭代对象）',
                        id: '2b277a220f58fec704e69cddd4770a03',
                      },
                      {
                        topic: 'Map and Set（映射和集合）',
                        id: '471dbf49c97f6a5d3ee5ce175487f15f',
                      },
                      {
                        topic: 'WeakMap and WeakSet（弱映射和弱集合）',
                        id: '18b47319c37dee157fdd512be58632c5',
                      },
                      {
                        topic: 'Object.keys，values，entries',
                        id: '9e1e9b0f2f3cffa33f0af9821035076d',
                      },
                      {
                        topic: '解构赋值',
                        id: '1acf6f71562b8c7f916b19218bd53bb4',
                      },
                      {
                        topic: '日期和时间',
                        id: '0a54024c5b25d44337ee936da303cf76',
                      },
                      {
                        topic: 'JSON序列化、反序列化',
                        id: '6b469d7db067f6b8d7de2974dd34db87',
                      },
                    ],
                  },
                  {
                    topic: '对象',
                    id: 'eb21dd3b6e7024f41ab41cb7e438d513',
                    children: [
                      {
                        topic: '属性/字面量',
                        id: 'aa25bee0e7804b3a64a3993d2a4ad641',
                      },
                      {
                        topic: 'in、for…in',
                        id: '93558df1d5a4c9e0fd35603697ef6c8e',
                      },
                      {
                        topic: '对象引用',
                        id: '768611c858dcb9acf444583504743862',
                      },
                      {
                        topic: '深拷贝、浅拷贝',
                        id: 'db2f67e46dd08b79bb7298954c9ea735',
                      },
                      {
                        topic: 'symbol',
                        id: 'c5fd1fbc454d7f98b40f89f27e376179',
                      },
                      {
                        topic: '垃圾收集机制',
                        id: 'e555428d9ad5907ae5e606e0e586e929',
                      },
                      {
                        topic: 'new',
                        id: 'e132ec0560e2e29149d3282f3a8ddace',
                      },
                      {
                        topic: "Optional chainang '?.'",
                        id: 'dceee85ccb695c21abe70838b751310e',
                      },
                      {
                        topic: '类型转换 Symbol.toPrimitive',
                        id: '5a20cedb58c5c05720305c8de129ff11',
                      },
                      {
                        topic: 'Property flags、descriptors',
                        id: 'be6dd2a5a34c8274f5dab17a16d68e02',
                      },
                      {
                        topic: 'getters、setters',
                        id: '58948107e951adc4bd193d312d05de3c',
                      },
                    ],
                  },
                  {
                    topic: '函数',
                    id: 'f5d245a990c6a95e1dac54cbac802252',
                    children: [
                      {
                        topic: '调用栈',
                        id: '3f3611484a07c9ff93b7641d4e924755',
                      },
                      {
                        topic: '递归、尾递归',
                        id: '593d5c64df0d49404c4975f975a3759e',
                      },
                      {
                        topic: 'arguments、params spread',
                        id: 'd0d5dfdd07a5bf8d9033231fee874e0a',
                      },
                      {
                        topic: '作用域、闭包',
                        id: '12a32a38ef411518163c7d068c2b5a79',
                      },
                      {
                        topic: 'var、变量提升',
                        id: 'a10f2b814fc50661b933c2eaee5ec03a',
                      },
                      {
                        topic: 'IIFE、匿名自执行函数',
                        id: '61144140504e5f2c9c9371a4617d0405',
                      },
                      {
                        topic: 'NFE、函数命名表达式',
                        id: '0d99c8755f14d990a22e95b691bae6c6',
                      },
                      {
                        topic: '箭头函数',
                        id: '2a060e2129ee0a48ced08ff406595b77',
                      },
                      {
                        topic: 'new Function',
                        id: 'af5bac9a9c4bfbc1f8cda0208a00bf66',
                      },
                      {
                        topic: 'setTimeout、setInterval',
                        id: '995d24f7421f9bfed77087906f63ea9b',
                      },
                      {
                        topic: 'call、apply、bind',
                        id: '5cec2a7aaac28f03a0b99622592deecb',
                      },
                      {
                        topic: '部分施用、柯里化',
                        id: '6727ab9aca260648d519617b63ec774e',
                      },
                    ],
                  },
                  {
                    topic: '原型',
                    id: 'a57c1ac925998d3a1709af867145f3ba',
                    children: [
                      {
                        topic: '原型链、继承',
                        id: 'ff4a651f8082f8760c48fcf0ca195d07',
                      },
                      {
                        topic: 'F.prototype',
                        id: 'ffd2544ac8383b5187a19c4a7bcb532b',
                      },
                      {
                        topic: 'Object.prototype',
                        id: 'b298a6e7c0f6984bef37d42207c0569d',
                      },
                    ],
                  },
                  {
                    topic: '类',
                    id: 'b55ea652d3ac3f9b49b0376a0bf0c8a6',
                    children: [
                      {
                        topic: 'extend继承',
                        id: 'eb437075af507cfd88efb88b65823750',
                      },
                      {
                        topic: '方法重载',
                        id: '24c7bec35e06690fd645d865cf0789c1',
                      },
                      {
                        topic: '构造函数',
                        id: 'e35b16c68b2ee8ee7de1f7abf9564949',
                      },
                      {
                        topic: 'Super',
                        id: 'cdfe600b9871a95cdb6a44c8682f6421',
                      },
                      {
                        topic: '静态属性、静态函数',
                        id: '2e2202de29086e6031afbb981e4a8bd6',
                      },
                      {
                        topic: '私有属性、私有函数',
                        id: '41a5751a2dad06920f4059e27d528034',
                      },
                      {
                        topic: '混合、Mixins',
                        id: '7a3f1a0bae895ab2ef916fcff9b3c06a',
                      },
                    ],
                  },
                  {
                    topic: '异步流程控制',
                    id: '9635b8e8b6839609350edc6fbfdf037a',
                    children: [
                      {
                        topic: 'Callback',
                        id: 'a32aaf62e735e949d56be3a559c8efd1',
                      },
                      {
                        topic: 'Promise',
                        id: '311117b9552bab053eabe8078420f18a',
                        children: [
                          {
                            topic: 'Promises/A+、Promisification、Thenable',
                            id: '8dcdec27e7348ec2d1aeadea3348541d',
                          },
                        ],
                      },
                      {
                        topic: 'async/await',
                        id: 'f3fd49874948d4631999d4e8f3a01084',
                      },
                      {
                        topic: 'generator',
                        id: '4614cd8e445a46065a461ee1a00a0f65',
                      },
                      {
                        topic: 'iterable',
                        id: '6a6fe61d80a8a4d4eb54ad1737619d67',
                      },
                    ],
                  },
                  {
                    topic: '模块化',
                    id: '84c969871d91cd733be52d993a4ecfca',
                    children: [
                      {
                        topic: 'commonJS',
                        id: '85d3564e62a319503fe0567a649a1a4f',
                      },
                      {
                        topic: 'amd、cmd、umd、es-module',
                        id: '8d11665e6de762ad3ef9650cf7592a11',
                      },
                    ],
                  },
                  {
                    topic: '异常捕获',
                    id: 'faa8788b7212148c8a52e32fa054c1be',
                    children: [
                      {
                        topic: 'try…catch…finally',
                        id: '5176c9d386faa08451a3576fa650270d',
                      },
                      {
                        topic: 'throw',
                        id: 'c237269343481a4c94caa74add720831',
                      },
                      {
                        topic: 'Error',
                        id: '8190a0e4f99d854dd9d8778524dd7603',
                      },
                    ],
                  },
                ],
              },
              {
                topic: '浏览器',
                id: '7f19816c5e19cf86230e5b226e5c9903',
                children: [
                  {
                    topic: 'DOM',
                    id: '50fb0ac8bda54a167800753e2c9ede64',
                    children: [
                      {
                        topic: 'DOM Tree',
                        id: '8554e27faf9ff46f06a1c358efe13f65',
                      },
                      {
                        topic: 'DOM Node',
                        id: 'b860f58b50b0d8ce79bbdf4d328d40f9',
                      },
                      {
                        topic: 'DOM Query',
                        id: 'ad858b6e6e34ca35ff5e0ed3167a9a4c',
                      },
                      {
                        topic: 'DOM Properties',
                        id: '69ef5006e88ceea8396a9ea48e8a8f60',
                      },
                      {
                        topic: 'DOM Modify',
                        id: 'eaf5537bcd7527895cd2dce509dc3a1d',
                      },
                      {
                        topic: 'Styles',
                        id: 'ed3af5bd2dbb9a8705cc4f0b9bc70c51',
                      },
                      {
                        topic: 'Coordinates、Element Scrolling',
                        id: '9abcdab601b97f1b6cd5f192239f6fd2',
                      },
                      {
                        topic: 'DOM Events',
                        id: '3fd53adf82a0249470adf92a98ccdc6c',
                        children: [
                          {
                            topic: 'UI Event',
                            id: '3dce9de8b79aa24336dd78a1e8459548',
                          },
                          {
                            topic: 'Bubbling and Capturing',
                            id: '2b91f8f03a56aeee4bf5f4edf00d2d74',
                          },
                          {
                            topic: 'Event Delegate',
                            id: 'eff3c7a9e6b24b8c3ca9468134f6a257',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '浏览器API',
                    id: 'a83b86afddaf3acfab38b1b548a8b5b9',
                    children: [
                      {
                        topic: 'location',
                        id: 'c85eb54b437e83bec467979ee254285f',
                      },
                      {
                        topic: 'history',
                        id: '4aa1e2d8fb13191af97c498af5dc72dd',
                      },
                      {
                        topic: 'navigator',
                        id: '040cb2a9a0f165a58808573deb18fadf',
                      },
                      {
                        topic: 'Default Actions',
                        id: '76be27391d41ce7c92f4301a293ea8ae',
                        children: [
                          {
                            topic: 'event.preventDefault()',
                            id: 'a38977899b13db542e83a3678540401a',
                          },
                        ],
                      },
                      {
                        topic: 'Form',
                        id: '057c635a775a8f3b788486ab26979648',
                        children: [
                          {
                            topic: 'change',
                            id: '02f28f4e87a1c0f129383f7502caa6a9',
                          },
                          {
                            topic: 'focus',
                            id: '33e83ab232b9c47a7a3fcfdb280ea46f',
                          },
                          {
                            topic: 'blur',
                            id: '2f6cdbcb173c7ede90c779ef370b03d6',
                          },
                          {
                            topic: 'submit',
                            id: '1e50adf7bd97e0fb66eaa37a4e9c8701',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '网络',
                    id: '647f828031265ccb7d014fb189e85fde',
                    children: [
                      {
                        topic: 'XHR',
                        id: 'e964900fab3b3d5aece93934516f974b',
                      },
                      {
                        topic: 'Fetch',
                        id: '702d3c50dd7025bb16de3415cccec6b2',
                      },
                      {
                        topic: 'JSONP',
                        id: 'dbe3ef65f80d932f5a4fac86713a944d',
                      },
                      {
                        topic: 'WebSocket',
                        id: '66255d2ff5cf5b3cfd59ca60b5c8a95e',
                      },
                    ],
                  },
                  {
                    topic: '权限',
                    id: '79ed95a09a79212c5fba03b90db5e4de',
                    children: [
                      {
                        topic: 'Cookie',
                        id: 'f4a471b142737acd0c0d335273545790',
                      },
                      {
                        topic: 'Session',
                        id: 'b09520ab4683e67812e674592fe7c8e8',
                      },
                      {
                        topic: 'OAuth',
                        id: 'b29ac332a04e751f5f5e7016e4d6c760',
                      },
                      {
                        topic: 'SSO',
                        id: '567b341317da97fd2f96e57fcf7ab629',
                      },
                      {
                        topic: 'JWT',
                        id: '8915f30e8652f65e5641b10bdf732387',
                      },
                    ],
                  },
                  {
                    topic: '安全与隐私',
                    id: '0561a37ca4bdf90af1b4e28c4dab09fe',
                    children: [
                      {
                        topic: 'Content Security Policy(CSP)',
                        id: '62dc832240ea38fd7e735df3d2b85ae5',
                      },
                      {
                        topic: 'CORS',
                        id: '0c0edc5e50d855f59b93dcf43cdb454a',
                      },
                      {
                        topic: 'XSS',
                        id: '41b3977744930437487a51af2e5d57cf',
                      },
                      {
                        topic: 'CSRF',
                        id: '341d42efc06186f3e661061091b28753',
                      },
                      {
                        topic: 'MITM',
                        id: '26d84d37dc2bd25609b84ecb774aae62',
                      },
                      {
                        topic: 'Samesite',
                        id: '9568b3941ad9f1aafdb426555366c308',
                      },
                    ],
                  },
                  {
                    topic: '兼容性',
                    id: '5e75065d21cafeccc7d96250a9053f12',
                    children: [
                      {
                        topic: 'Can I Use',
                        id: '31e989d52093100e430bd8a2021eb23c',
                      },
                      {
                        topic: 'polyfill',
                        id: 'eed7069c257fbe0d2a1e66a867816471',
                      },
                      {
                        topic: 'shim',
                        id: '838f4d98c56e6fb2f12ecef617968de3',
                      },
                      {
                        topic: 'browserslist',
                        id: 'c603f00c20df4a146b2f1f283728d606',
                      },
                      {
                        topic: 'Autoprefixer',
                        id: '********************************',
                      },
                    ],
                  },
                  {
                    topic: '开发者工具',
                    id: 'b57be7204ee4a49d9bb0ccce74b54cb4',
                    children: [
                      {
                        topic: '设备模式',
                        id: '248d6c05787fca135ac5a0df01477678',
                      },
                      {
                        topic: '元素面板',
                        id: '83621ba6019b0e4bb843f0b249406f4e',
                      },
                      {
                        topic: '控制台面板',
                        id: '8416c48149eb927c996747695e425198',
                      },
                      {
                        topic: '源代码面板',
                        id: '61eca01c459db4cc5bfc507e6431bd28',
                      },
                      {
                        topic: '网络面板',
                        id: '3f27665f18496ab0ada8aaaa22a421c1',
                      },
                      {
                        topic: '性能面板',
                        id: 'ad642f8dd2c8f57467a550c29564c239',
                      },
                      {
                        topic: '内存面板',
                        id: 'db0ba2995224f12098fc8fa34cae79fa',
                      },
                      {
                        topic: '应用面板',
                        id: '87ec9ef16c68e85ddec0cd2be2a581b3',
                      },
                      {
                        topic: '安全面板',
                        id: 'ba5567a6f9296fe63c9554ab2f76dc3a',
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            topic: '中阶',
            id: '3dab1ad6cc0a304a63b2fd2e736d4861',
            children: [
              {
                topic: '研发链路（工程化基础）',
                id: '2ae299b85b670554cfce102177e66498',
                children: [
                  {
                    topic: '脚手架（Scaffold）',
                    id: 'ae967d0b7db430d4f2500b1a45e5b810',
                    children: [
                      {
                        topic: 'CLI(command-line interface)',
                        id: 'a4e1b207e07f3b92f797ff18b3514823',
                        children: [
                          {
                            topic: 'commander',
                            id: '2b911b6446423721f1d2b1e634d544ad',
                          },
                          {
                            topic: 'inquirer',
                            id: '7ff61b7d790f1d33d8d98f4d97a67d15',
                          },
                          {
                            topic: 'ora',
                            id: 'f4f3d645ce19d43eb91a32d10a624953',
                          },
                          {
                            topic: 'chalk',
                            id: '36049a32603132d40089215d070ff7c4',
                          },
                          {
                            topic: 'emoji',
                            id: '2e1b20b69bc2f974b66ddc2eee58a929',
                          },
                        ],
                      },
                      {
                        topic: '初始化（Boilerplate）',
                        id: '784c8e54adf535d95078bfcf258e36db',
                        children: [
                          {
                            topic: 'web-cli',
                            id: 'c10442e7e2be234a10d8e568c7ee5df2',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '包管理',
                    id: '9b42b61be6eff55092546bb67cf3730b',
                    children: [
                      {
                        topic: 'ttnpm',
                        id: '360dc236fc03d03aa81988ba1caa79e2',
                      },
                      {
                        topic: 'NPM',
                        id: '572a4d7d15073762aea72014040f777a',
                      },
                    ],
                  },
                  {
                    topic: '开发',
                    id: 'f9a0f97795a18ffced53f8c4694fcd63',
                    children: [
                      {
                        topic: 'dev-server',
                        id: '10375b9ff9c08af47d4898a9509a8511',
                      },
                      {
                        topic: 'hot-reload',
                        id: '8adaffd0b7d59d357149914760ccd73c',
                      },
                      {
                        topic: 'mock',
                        id: '2b00b2e870d7163a91499a4b8e346eee',
                      },
                      {
                        topic: 'proxy',
                        id: '95daf738dd16cc9d7739415f7dacfb3c',
                      },
                    ],
                  },
                  {
                    topic: '构建',
                    id: '519ce842acb7cd2cf5f605e6f9dfec2d',
                    children: [
                      {
                        topic: '构建器',
                        id: 'a86498be1fbc7c4ab90869e34c0d4174',
                        children: [
                          {
                            topic: 'webpack4、webpack5',
                            id: 'c547e824103e8843ddd95875f0d885af',
                          },
                        ],
                      },
                      {
                        topic: 'JS编译',
                        id: '3cc185a71adb3e24f16b916c74915c6e',
                        children: [
                          {
                            topic: 'babel',
                            id: '50f530a5967f5d5c0edb5e5b57adc706',
                          },
                          {
                            topic: 'esbuild',
                            id: '59cdf6329433e00ead52e2f69160391f',
                          },
                        ],
                      },
                      {
                        topic: 'CSS编译',
                        id: '2c093b47f06831df1ab239d840834ed7',
                        children: [
                          {
                            topic: 'Less',
                            id: 'b0e1e4e7c28dc43587ab4ca06b1ab62a',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '代码规范',
                    id: '1bb398caadd2920445208550ebe590ba',
                    children: [
                      {
                        topic: 'TT前端组-前端规范',
                        id: 'ae37e760a1e441a01cbedcb972ad9ee4',
                      },
                      {
                        topic: '工具',
                        id: 'a9c319f5418cbfff14128d9423069254',
                        children: [
                          {
                            topic: 'ESLint',
                            id: '339eb46d1e67634fb4b41867fb4225c4',
                          },
                          {
                            topic: 'commitlint',
                            id: '428a9e031cb7ca5995bbd8b736de780d',
                          },
                          {
                            topic: 'Prettier（代码格式化）',
                            id: '43ed1b59190fcc52b307d8a104bc3c57',
                          },
                          {
                            topic: 'husky和lint-staged（流程控制）',
                            id: '7600d19bfc422beae7d446f53ded1607',
                          },
                          {
                            topic: 'CI-ESLint',
                            id: '290c4d7379de26244c011c6b9c9992cc',
                          },
                        ],
                      },
                      {
                        topic: '开发分支规范',
                        id: '20147645846e4f6671ef6e3ae5637cf6',
                      },
                    ],
                  },
                  {
                    topic: '测试',
                    id: 'ffbc34ceb328a27c8c1153ca421edb5f',
                    children: [
                      {
                        topic: '单元测试',
                        id: '1425b3f6e4cb79ff48106f8183c483fd',
                        children: [
                          {
                            topic: 'jasmine',
                            id: '7a2d127bdf9d6cf6b71a0e839c6dd5ec',
                          },
                          {
                            topic: 'mocha',
                            id: '58d289eaba7e388386d40de596897f80',
                          },
                          {
                            topic: 'jest',
                            id: '892a99e973240c35b6f9a32584582a54',
                          },
                          {
                            topic: 'enzyme',
                            id: 'c01406cb50c9d3ed1d6b0c74b3e8c9a9',
                          },
                        ],
                      },
                      {
                        topic: 'E2E测试',
                        id: 'd730ba6c2322e065813e814683a2c815',
                        children: [
                          {
                            topic: 'Selenium',
                            id: '3015ac3d28939964079b23f46849356b',
                          },
                          {
                            topic: 'karma',
                            id: '56d6e18fcc39af3f7b635385811615e3',
                          },
                          {
                            topic: 'cypress',
                            id: '4b2543a176c33c16149b8b4937e12cd5',
                          },
                          {
                            topic: 'Puppeteer',
                            id: '81514e7bcbd7a369c66f1a16abd1b2ca',
                          },
                          {
                            topic: 'Appium',
                            id: '5d067c85e90064e84f3672135fb6a9f9',
                          },
                        ],
                      },
                      {
                        topic: '覆盖率测试',
                        id: '5cb2fd430495f8020f24b678a4a9b7b8',
                        children: [
                          {
                            topic: 'istanbul',
                            id: '6797e27599396f0251e289c33249f2df',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: 'CI/CD',
                    id: 'b191b7ea48234abb1a63b1f2779dcaa8',
                    children: [
                      {
                        topic: 'gitlabCI',
                        id: '621ea561869a20cc81c14c14377b875b',
                      },
                    ],
                  },
                ],
              },
              {
                topic: '库',
                id: '1e82b5f5ab531e1073719e975f33429a',
                children: [
                  {
                    topic: '原则',
                    id: 'faaeffd9d67802fc22d9d1f81d1e01ad',
                    children: [
                      {
                        topic: 'DRY',
                        id: '62f0d13aff117601c6ddc48b46181833',
                      },
                    ],
                  },
                  {
                    topic: 'CSS',
                    id: '177ed00834e2011f9c5c2b707506c323',
                    children: [
                      {
                        topic: '作用域',
                        id: '99562e5cf75b554be4226c60a6f525e3',
                        children: [
                          {
                            topic: 'scoped css',
                            id: '93b43cb55ff303fb3493825f104b006b',
                          },
                          {
                            topic: 'css modules',
                            id: '478e64c435a812d0be70f100c74c86db',
                          },
                          {
                            topic: 'css-in-js',
                            id: '5840ef7506ded776617110ff721b4ab9',
                          },
                        ],
                      },
                      {
                        topic: '样式库',
                        id: 'de12748b366de01957d4428f47b78c3b',
                        children: [
                          {
                            topic: 'normalize.css',
                            id: '537244e9ac9cecb2bdf964fbe6e14e91',
                          },
                          {
                            topic: 'Bootstrap',
                            id: 'fa8d61fb035c3e5bdfb93a9194456d92',
                          },
                          {
                            topic: 'Tailwind',
                            id: '38c81c9026fe9b0d929e10e704d75e47',
                          },
                          {
                            topic: 'Bulma',
                            id: '6ff2a187b2906ce4e3c0c0881460385e',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: 'JS',
                    id: '9eb5f339684546659e43c37fa84b94d9',
                    children: [
                      {
                        topic: '工具类',
                        id: 'a74d8d8ddef62914230d265f96551a5b',
                        children: [
                          {
                            topic: 'history',
                            id: '29249c36dc58e342317b2f6cc851d8e6',
                          },
                          {
                            topic: 'path-to-regexp',
                            id: 'acf5213db738b84f688f084894779fb3',
                          },
                          {
                            topic: 'lodash',
                            id: 'f4a7852a40a2aff345e0790efc2c5319',
                          },
                          {
                            topic: 'fastclick',
                            id: '85b17cf44c8916c2b796839e4caf2b22',
                          },
                          {
                            topic: 'date-fns',
                            id: '9209eaab16f9544415c2d388b799b78d',
                          },
                        ],
                      },
                      {
                        topic: '网络',
                        id: '8bbf8c792114a9faf945646911d0267a',
                        children: [
                          {
                            topic: 'axios',
                            id: '20c5fd823f78d651d612ed3c498e67f3',
                          },
                          {
                            topic: 'got',
                            id: '040d53150f620e84f8998ebf86744a5a',
                          },
                        ],
                      },
                      {
                        topic: '数据流',
                        id: '9e4c9d13ada866de825693b2755033e3',
                      },
                      {
                        topic: '模板引擎',
                        id: 'a37f5f767d7c9a6c0abfc62d7cb3eecc',
                      },
                    ],
                  },
                  {
                    topic: '动画',
                    id: '815e6e8a893a0d8fa8c1e980684c60b8',
                    children: [
                      {
                        topic: 'CSS动画',
                        id: '0796d1461805d65c54dc7262670d746e',
                      },
                      {
                        topic: 'JS动画',
                        id: 'e35945635420a2017514d831ca5a0f70',
                      },
                      {
                        topic: 'Lottie',
                        id: '1a69ba89ba00d653afd8a583cb69df3d',
                      },
                    ],
                  },
                  {
                    topic: '设计规范/组件',
                    id: '89cf39097e4964081615e4b07793108c',
                    children: [
                      {
                        topic: 'Material Design',
                        id: '0ae21932ffcb23a5277521e3ef4e7711',
                      },
                      {
                        topic: 'Apple Human Interface Guidelines',
                        id: 'ace744986fbb79d24c9bc8ba40355609',
                      },
                      {
                        topic: 'Ant Design',
                        id: '75a835435d3bdb49abb9df7f4667366e',
                      },
                      {
                        topic: 'WeUI',
                        id: 'cb28b53ac9d0eda9dc3bd2eecbabea08',
                      },
                    ],
                  },
                  {
                    topic: '文档',
                    id: '5c9dd0f1a7745281547d3d048742a870',
                    children: [
                      {
                        topic: 'jsdoc',
                        id: '1e7e0c6315c4691db24d7fad108fd479',
                      },
                      {
                        topic: 'bisheng',
                        id: '4b17d1b2c39cfec00d6a865035b90d65',
                      },
                      {
                        topic: 'dumi',
                        id: '0bb6376505ec4e878e34bcee5cad9ff4',
                      },
                      {
                        topic: 'Storybook',
                        id: 'd1be68dcb7cf7ac978dc9d0f73664d40',
                      },
                    ],
                  },
                ],
              },
              {
                topic: '框架',
                id: 'fedfca839fe630f15e208234fbb4efd1',
                children: [
                  {
                    topic: 'Vue2',
                    id: 'dcd03a8e504486de38e05eefeaf80015',
                  },
                ],
              },
              {
                topic: '性能优化',
                id: '51ca35304e8d0cc20349cf95fdd00578',
                children: [
                  {
                    topic: '指标',
                    id: '0b0487fa55b13f889ec82c6d9a6949ff',
                    children: [
                      {
                        topic: '真实指标',
                        id: '2a27f76a97e70cce8a2276ee5dba4bd2',
                        children: [
                          {
                            topic: 'First Contentful Paint (FCP)',
                            id: '9b6f6151a03868ab7fea73afe4fd2d3b',
                          },
                          {
                            topic: 'Largest Contentful Paint (LCP)',
                            id: '0732c25579333b74b1adac9caa0acb3f',
                          },
                          {
                            topic: 'First Input Delay (FID)',
                            id: 'f4c8e18b9bed53370ca4c53d85de8b20',
                          },
                          {
                            topic: 'Cumulative Layout Shift (CLS)',
                            id: '10ffd1bc12012bb0dc5963fdd2b4cee9',
                          },
                        ],
                      },
                      {
                        topic: '实验室指标',
                        id: '6daf73b6ab9b9dbf157b4a77fa4ca701',
                        children: [
                          {
                            topic: 'Total Blocking Time (TBT)',
                            id: '811cd2448dd68094a115e2b73f75600e',
                          },
                          {
                            topic: 'Time to Interactive (TTI)',
                            id: '1eb9219a52a81a9ed483b33d6e655801',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '评估工具',
                    id: '2c33f9fd71e7a7679ebfbad2baef23db',
                    children: [
                      {
                        topic: 'Chrome DevTools',
                        id: '45f118578ce1cf703cfa1fe77ef411d4',
                      },
                      {
                        topic: 'LightHouse',
                        id: '82f10acd96224e425778339de88b5933',
                      },
                      {
                        topic: 'PageSpeed Insights',
                        id: '053826ca4ed651379f3d41540fe9e165',
                      },
                      {
                        topic: 'WebPageTest',
                        id: 'e3157f88f1de5957d5b02295209e452c',
                      },
                    ],
                  },
                  {
                    topic: '优化方案',
                    id: 'a5080132452fa769fc21812a28894407',
                    children: [
                      {
                        topic: '压缩',
                        id: '29ab185cc63d297acd830021be38ef54',
                        children: [
                          {
                            topic: '代码压缩',
                            id: 'a7020c0259a0029dc333b7e1f50f7804',
                          },
                          {
                            topic: '文本压缩（gzip、Brotli、Zopfli等）',
                            id: 'fa82f5357a83b265da79d0036a6dfe08',
                          },
                          {
                            topic: 'Tree-shaking',
                            id: 'bb5511548ff87c19eb13b2515cacdc4c',
                          },
                          {
                            topic: 'Code-splitting',
                            id: '7e2482c5f9a0bbf3268fe50ce1113a13',
                          },
                        ],
                      },
                      {
                        topic: '图片优化',
                        id: '95313d4d58011c7ae746bd65ee76b85c',
                        children: [
                          {
                            topic: '小图优化（css sprite、iconfont、dataURI、svg）',
                            id: '084d8b04cec493c748de7a31453e1162',
                          },
                          {
                            topic: '图片格式选择',
                            id: 'ffbe4b15921508d7cd048605dcf0f094',
                          },
                          {
                            topic: '压缩（如tinypng）',
                            id: '794306da6f9761f10cb84adf4a9fc262',
                          },
                          {
                            topic: '响应式',
                            id: '6488f4a87096005deac5c73c74f65c56',
                          },
                        ],
                      },
                      {
                        topic: '加载策略',
                        id: 'ee3bf09993748edb68b09318558e57cc',
                        children: [
                          {
                            topic: '懒加载',
                            id: '21f0a42128f0feba272accedec8afc40',
                          },
                          {
                            topic: 'DNS预解析、预加载、预渲染',
                            id: '96adfb2a34ee1eb1a1d24f3cc56e5451',
                          },
                          {
                            topic: '离线化（ServiceWorker、AppCache、离线包等）',
                            id: '2da2cd04f2f347bf6c6894faba1f53ea',
                          },
                          {
                            topic: 'HTTP缓存',
                            id: '4744297336c2984ab847a4b1b9e95770',
                          },
                          {
                            topic: '数据缓存（localStorage、sessionStorage）',
                            id: '4ca3771548ba02ced11b41fc95403236',
                          },
                          {
                            topic: '资源加载（顺序、位置、异步等）',
                            id: '503cafbf035aea23fce763bf14efcbc6',
                          },
                          {
                            topic: '请求合并',
                            id: 'caadd3076a0b5a61543329c963e45a93',
                          },
                          {
                            topic: 'HTTP2',
                            id: '0075b823d291aed1438e4cd050a7d2dc',
                          },
                          {
                            topic: 'CDN',
                            id: 'f624f142e817c764306aa95aed7271f0',
                          },
                          {
                            topic: '服务端渲染',
                            id: 'e324249eef67a08b1796e53a0cbb09d1',
                          },
                        ],
                      },
                      {
                        topic: '执行渲染',
                        id: 'b75fc104a026f22f7d72d9e9c9cf37d7',
                        children: [
                          {
                            topic: 'CSS代码优化（选择器、启用GPU、避免表达式等）',
                            id: '4fd87681f0a8457a8fb6dfe054ae1742',
                          },
                          {
                            topic: 'JS代码优化及评估',
                            id: 'fcf23b177efe6d89cff34038938865ec',
                          },
                        ],
                      },
                      {
                        topic: '感官体验优化',
                        id: 'cabef7a1790745be388bb65731fde91d',
                        children: [
                          {
                            topic: '骨架屏',
                            id: '73ee0a67aff6ef6f4b24c5b5b93e25b1',
                          },
                          {
                            topic: 'Snapshot',
                            id: '008054d0d8cf52009d1bb5056f86d330',
                          },
                          {
                            topic: 'Loading',
                            id: 'cdb6a058e9f136a47dc7d83be4a6364d',
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                topic: '工作原理',
                id: 'ad56aaeccc79d3fe46143fd8c3465a7d',
                children: [
                  {
                    topic: '浏览器',
                    id: '365c48febbd3ae0aaeeac97365ab27b0',
                    children: [
                      {
                        topic: 'DOM Tree、CSSOM',
                        id: 'b8c63ab9f1d34f3b6d1c2b64e204889e',
                      },
                      {
                        topic: '渲染、绘制',
                        id: '0fea40acc0c8f55cbef6a7d7970132d1',
                      },
                      {
                        topic: '会话',
                        id: '0590d71564588164d9306483f987e452',
                      },
                      {
                        topic: '事件循环',
                        id: '681a3c21166d96ee3e8dd8fc4350360d',
                      },
                      {
                        topic: '垃圾回收',
                        id: '1ba061f8fd7a89280cdd6753e6891164',
                      },
                      {
                        topic: 'Webkit深入',
                        id: 'c37283e0af6974be953858937e218ba3',
                      },
                    ],
                  },
                  {
                    topic: 'JavaScript引擎',
                    id: 'aa74256a4aec487dcfe538f3bbb5239d',
                    children: [
                      {
                        topic: 'V8',
                        id: '674e91625a1d029024dacdc672a52877',
                      },
                      {
                        topic: 'SpiderMonkey',
                        id: 'da40ba12af5405b19ccd745a1f5bf527',
                      },
                      {
                        topic: 'JavaScriptCore',
                        id: 'a37ba000ef7fe729c38ae4914ebefe59',
                      },
                    ],
                  },
                ],
              },
              {
                topic: '综合能力',
                id: '0f703794664149ee6926c0b18423a219',
                children: [
                  {
                    topic: '知识管理',
                    id: '9ce178358ae5a94454723ac547b9237a',
                    children: [
                      {
                        topic: 'Markdown',
                        id: 'f6ad905c127f0efecd2cf1892cd5fa57',
                      },
                      {
                        topic: '脑图',
                        id: '2fc4fb72eaebe23e27ab1212e0bead26',
                      },
                      {
                        topic: 'wiki',
                        id: '893d4cf7f49ee7e81e9e39b804277b27',
                      },
                      {
                        topic: 'GitBook',
                        id: '43dd4578d4201ae1154cc8aa11bc2762',
                      },
                    ],
                  },
                  {
                    topic: '软件工程',
                    id: '56935896bf29a34f2f1cd9cca32493fb',
                    children: [
                      {
                        topic: '过程模型',
                        id: '78f9a3cad498a4fedd4df213a724dc75',
                      },
                      {
                        topic: '需求分析',
                        id: 'ab6fedc0cae7fc302b5580510eb01cee',
                      },
                      {
                        topic: '功能拆解',
                        id: 'dc99b5db199760b90fb5aa6cc9719db5',
                      },
                      {
                        topic: '概念设计',
                        id: 'd28e544a006c44f51cf6ccd386cb7ed4',
                      },
                      {
                        topic: '体系结构设计',
                        id: '691fc73598cf0aa2577de1a6570b9109',
                      },
                      {
                        topic: '项目管理',
                        id: '37e7405289a8db9200d849d71f8c7cfa',
                      },
                    ],
                  },
                  {
                    topic: '交互设计',
                    id: '6de8ad4326ed1cfdd93de62aed9a7566',
                    children: [
                      {
                        topic: '交互原型',
                        id: '02e0790d3269fbf4db0a8889cbb9d400',
                      },
                      {
                        topic: '视觉还原',
                        id: '899c98c045bb6308394e17db614d93a9',
                      },
                    ],
                  },
                  {
                    topic: '开源项目',
                    id: '6853b82065bf562d614e907465fbc774',
                    children: [
                      {
                        topic: 'GitHub',
                        id: '38f8580f5a829dbf3ee4309536555201',
                      },
                      {
                        topic: 'OpenJS',
                        id: '42c78c964ad07fa3e0419442fcedd572',
                      },
                      {
                        topic: 'Apache',
                        id: 'a2b1bef6566c6730fa5c07b8b51553ff',
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            topic: '高阶',
            id: '6527c324e340362ad18e4104676f6199',
            children: [
              {
                topic: '跨端技术',
                id: '3549da5e57adb71515c7c38327f8a7db',
                children: [
                  {
                    topic: '跨端解决方案',
                    id: '81dab7d787faf1351e584bf77ab24f00',
                    children: [
                      {
                        topic: '跨平台',
                        id: 'e2abeed2788cfbc980a8864b93f66841',
                        children: [
                          {
                            topic: 'Web',
                            id: 'a5b15aa73142d70481d76ae53d755feb',
                          },
                          {
                            topic: 'Electron',
                            id: 'e9e4b52c3f602751935b127955a8a7ab',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '跨端API',
                    id: '24c470a6493ad8f518b3776cdb7bf7ce',
                    children: [
                      {
                        topic: '桥接与通信',
                        id: '0e3290a8c2ac52d822392cbf64611686',
                        children: [
                          {
                            topic: 'JSBridge',
                            id: 'cd40ae824180818229d9e82b392de19a',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '跨端组件',
                    id: '13596ca21b764ee0e8a5599c25d487f5',
                    children: [
                      {
                        topic: '跨容器（H5/小程序）',
                        id: 'c03dfe8d5e863a44e7b41268caf16b77',
                      },
                      {
                        topic: '跨平台（PC/移动）',
                        id: '83a29ee9001712927d64b64f84274443',
                      },
                      {
                        topic: '视觉交互',
                        id: '6e0d506f9f54711bb93010c14c42f11a',
                        children: [
                          {
                            topic: '自适应',
                            id: '7c5a93d376ef9a21629fa63a3a5b2a49',
                          },
                          {
                            topic: '平台特性',
                            id: 'd24d1e3dc997faea79c0f1a6d9c6bccb',
                          },
                        ],
                      },
                      {
                        topic: '标准与规范',
                        id: 'c11d704799c96d5e59e9c3229f492ddd',
                        children: [
                          {
                            topic: '脚手架',
                            id: '0f689aed294224b4f68ff74848720ab0',
                          },
                          {
                            topic: '文件结构',
                            id: '54c6565ba12d98aa163eb238d7a89422',
                          },
                          {
                            topic: '属性与API',
                            id: 'db78621bc0d94608faaa29a93e0cd220',
                          },
                          {
                            topic: '发布于引用',
                            id: '2c201e010f8852ed4591fa6922365f36',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '跨端搭建',
                    id: '36690d347154cd0840879d1280d52628',
                    children: [
                      {
                        topic: '一码多搭',
                        id: 'fc690f5154bfba078064192b00f3dcfc',
                        children: [
                          {
                            topic: 'web',
                            id: 'ca789e447d0328fd3ac69b95272e496d',
                          },
                          {
                            topic: '小程序',
                            id: '6b01e6b617f91afe33c8e33f8a39554c',
                          },
                          {
                            topic: 'Native',
                            id: 'd93b9e06fee8d48643d462c5a338601c',
                          },
                        ],
                      },
                      {
                        topic: '统一发布',
                        id: '0d30c0f2656b5cec10aeafe6c2cf0601',
                      },
                    ],
                  },
                ],
              },
              {
                topic: 'Node.js',
                id: 'c2261c54738566922c813975db535159',
                children: [
                  {
                    topic: '分布式服务',
                    id: '2c2c878fd1e253e307e4162ba7b9616b',
                    children: [
                      {
                        topic: 'rpc',
                        id: 'eccfe890dc85a9ad83e941606f0ef873',
                        children: [
                          {
                            topic: 'grpc',
                            id: 'd8cdcbb0cfe15cbfb1f35a4bf13b66ae',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    topic: '常用API',
                    id: 'b1350d56ba4e76e0ca1936f7367d5660',
                  },
                ],
              },
              {
                topic: '多媒体',
                id: '839ee6335a18164b309d3d5e7d93adae',
                children: [
                  {
                    topic: '音视频基础',
                    id: '4076b24b33ff293d8a0855ed5047a609',
                    children: [
                      {
                        topic: '基础概念',
                        id: '8c9ad408d51007d63deaaf9c987ab36a',
                      },
                      {
                        topic: '容器格式',
                        id: 'bb6bee0681624319c07aced9f7e00c82',
                      },
                      {
                        topic: '编码格式',
                        id: '915f0871f5eb321622b15754ecddaaba',
                      },
                    ],
                  },
                  {
                    topic: '直播技术',
                    id: '10d82739b3b32cf0e425ede164967be5',
                    children: [
                      {
                        topic: '推流',
                        id: '47cb6fb60a92923b7c77f1c9e98c89b9',
                      },
                      {
                        topic: '流媒体协议',
                        id: '20cedcae59f41321d933c1dc545e71e6',
                      },
                      {
                        topic: '流媒体服务',
                        id: '75991e70828d220b0420629f53cbcbd1',
                      },
                    ],
                  },
                  {
                    topic: '播放器技术',
                    id: '1a61194a3cb1fa3ce5e60d4ecaa22ca9',
                    children: [
                      {
                        topic: '拉流',
                        id: 'ecfe1785fae0bcbcc71c62fa87605cb2',
                      },
                      {
                        topic: '解码',
                        id: '1ca0119a12f15e7859e12f3cb5832b9a',
                      },
                      {
                        topic: '渲染',
                        id: 'fc644eabfab13c4e6aaaf20d369b81e4',
                      },
                    ],
                  },
                  {
                    topic: 'web媒体技术',
                    id: '976838b00e93bccb24620cb6465a1f2f',
                    children: [
                      {
                        topic: '流操作基础',
                        id: '522221bc1e20c1baf233a0430aad39da',
                      },
                      {
                        topic: 'webRTC',
                        id: '89d488a88be4e55bcc2d398e25ea386d',
                      },
                    ],
                  },
                  {
                    topic: '开源产品和框架',
                    id: 'f4ddb04d444c13d2eae67a9877650954',
                    children: [
                      {
                        topic: 'FFmpeg',
                        id: 'ab832d8f16548a33c90e35927fb1db62',
                      },
                      {
                        topic: 'video.js',
                        id: '7c98ff619e7d92486d4a032a61e443b9',
                      },
                      {
                        topic: 'OBS',
                        id: '752aca34a3c3e0b483dae0780e568ee4',
                      },
                    ],
                  },
                ],
              },
              {
                topic: '互动技术',
                id: '32b1123a7edc479f057186cca17b435d',
                children: [
                  {
                    topic: '基础知识',
                    id: '86e9651fc995018f78664a6b235029b1',
                    children: [
                      {
                        topic: '图形学',
                        id: '7b4dfa79449abc1e72eadec495c3e7f1',
                      },
                      {
                        topic: '数学',
                        id: '1de64d571ac18c822729f321fd813bd0',
                      },
                      {
                        topic: '物理学',
                        id: 'c1d07d6b320e8d74ed18c6efe00b8832',
                      },
                    ],
                  },
                  {
                    topic: '技术标准',
                    id: '021a216bab28643c7ae3eb66fdedb3ce',
                    children: [
                      {
                        topic: 'canvas',
                        id: '9ed359eecfce8d886dd0ddd49ef129ad',
                      },
                      {
                        topic: 'svg',
                        id: '92c9518bc8baae37b8cb36a9e0908bcf',
                      },
                      {
                        topic: 'css',
                        id: '21c116e1e4ec16df225de7fc1e133934',
                      },
                      {
                        topic: 'webGL',
                        id: '5ccc3acc657a3151462a543528110d89',
                      },
                      {
                        topic: 'webGPU',
                        id: '533ee82fe926eb8b370c5290be58c206',
                      },
                      {
                        topic: 'webAssembly',
                        id: '7ddb090acb799793423641b7a1f54feb',
                      },
                    ],
                  },
                ],
              },
              {
                topic: '智能化',
                id: '02440f45cc67563592131daf8d74f0f9',
                children: [
                  {
                    topic: '低代码/无代码',
                    id: 'a27b2dec76370106ba5a5ddce1962118',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        topic: '通用能力',
        id: '343e0f14c8ee44637cd86cde48d25e05',
        children: [
          {
            topic: '项目管理',
            id: 'e5e9de48e2f902435bada49db9d24741',
            children: [
              {
                topic: '项目范围说明',
                id: 'd372b41bcb8d4138a9a23c1d0b62628f',
                children: [
                  {
                    topic: '目标',
                    id: '430853ecd916c0b87568da04ab2af99a',
                  },
                  {
                    topic: '主要范围',
                    id: '5cb19cfc83498f4280c1ba4101bf7cec',
                  },
                  {
                    topic: '灵活性矩阵',
                    id: '19001933fb274382c9361c86b1c2354c',
                  },
                  {
                    topic: '风险范围及应对',
                    id: 'f1d68ac1080bbb9735eb494914937525',
                  },
                  {
                    topic: '核心成员',
                    id: 'd8e71b52e857aa891cf89db2f47af471',
                  },
                  {
                    topic: '里程碑、交付物、时间节点',
                    id: '06f2484f01377b17b9b218163f9f7184',
                  },
                  {
                    topic: '交付检查及复盘',
                    id: 'fc898637e0b84235ef2946684847694e',
                  },
                ],
              },
              {
                topic: '关键能力',
                id: 'aa495048cd155b233f74f7ab3a1c2c56',
                children: [
                  {
                    topic: '在他人指导下进行计划跟踪和监控',
                    id: '520bacbda2c4f1e229ab0bcf3755a2bd',
                  },
                  {
                    topic: '组织实施小型项目',
                    id: 'c63dd05343f3982b57688e1a6ef2c36c',
                  },
                  {
                    topic: '独立负责中型项目的实施和运作，预见部分潜在问题',
                    id: 'ac2db536111b625871f508996b8ec74b',
                  },
                  {
                    topic: '独立负责中大型项目，充分预见潜在问题',
                    id: '5aa50996e821a5897042a228d285a1fe',
                  },
                  {
                    topic: '独立负责过多个中大项目的实施和运作，进行风险控制',
                    id: '80c8aa9b0f417efac7caa6113311550d',
                  },
                ],
              },
              {
                topic: '管理者',
                id: '91262d97aa6873bae0baa6b730d90d5c',
                children: [
                  {
                    topic: '时间观',
                    id: '7471702dfa2da8656fd542674746f46b',
                  },
                  {
                    topic: '人际能力',
                    id: '2129aab6ba98832fa6c2e9bd7ac46b44',
                  },
                  {
                    topic: '灵活性',
                    id: 'd76b430dab23c397d1d95b5431273359',
                  },
                ],
              },
            ],
          },
          {
            topic: '沟通协作能力',
            id: 'bf11d24c53f642429e1dc6b61105f130',
            children: [
              {
                topic: '清晰表述，参与交流',
                id: '61e03b790366d19805da16dac5f5a657',
              },
              {
                topic: '简练表达，达成共识，赢得合作、沟通反馈',
                id: '320012f3c84a1fe4bc31d034a57a5456',
              },
              {
                topic: '换位思考，跨业务线沟通',
                id: 'aa89fd4a320a02c8f816bcfca65d7f39',
              },
              {
                topic: '解决分歧，获取资源，跨越合作',
                id: '2374ce1512bbcbe30519e9c37fc2d610',
              },
              {
                topic: '提升团队沟通能力，重大问题有效沟通',
                id: '57334267eaa0d11b5d6c6c49444b467f',
              },
            ],
          },
          {
            topic: '解决问题能力',
            id: '293ebf83c2c57a09ff98a4dd3127c5f5',
            children: [
              {
                topic: '发现问题，避免重复犯同样错误',
                id: '7a6181347559ac9bb354c705636a3ccb',
              },
              {
                topic: '发现核心问题，协助解决问题',
                id: 'b50d03cf086e1a35d7bc3ac853d81ab7',
              },
              {
                topic: '独立解决问题，提供解决方案',
                id: '4e0db9aa9caca411f3d1e338f1c3a33d',
              },
              {
                topic: '拆解复杂问题，理清因果逻辑',
                id: 'dd6ce6a6d695075e89ce5c43cc54577f',
              },
              {
                topic: '多维度思考，规避潜在风险',
                id: '91087753f66e557a32d2c3349997a2f9',
              },
            ],
          },
          {
            topic: '学习与发展能力',
            id: '61248e9229d37600ae2a4efabc723497',
            children: [
              {
                topic: '在他人指导下学习',
                id: 'de50dab04874e5da2ed9e9157c15ab5a',
              },
              {
                topic: '主动寻找学习机会，注意学以致用',
                id: '40f9f1b3c84c8624c6f11961e6a7614a',
              },
              {
                topic: '高效学习技巧，乐于分享辅导',
                id: '4669d40e60a880c74833238f092907e2',
              },
              {
                topic: '了解前沿技术，沉淀知识体系',
                id: '6307dfc630e00f2abd46e5095c764c9a',
              },
              {
                topic: '营造学习氛围，培养接班人',
                id: '54db81bab8959d9634f37a37ab5a3eb3',
              },
            ],
          },
          {
            topic: '业务导向',
            id: '2c7d19a08a59693576d4d013b490a712',
            children: [
              {
                topic: '清楚岗位角色、了解业务流程',
                id: 'fd53f7ae68fc287389820bc3a60c2f38',
              },
              {
                topic: '熟悉业务逻辑，识别核心关键',
                id: '718c1f3d937e07f27b5869a48b181272',
              },
              {
                topic: '熟悉商业模式，推动业务发展',
                id: '4a4c55c9897a6f00aaed11741d4a41e1',
              },
              {
                topic: '了解行业趋势，未来发展建议',
                id: '71eecdc4bd46e4d7eb03550f2627d847',
              },
            ],
          },
        ],
      },
    ],
  },
}

export default frontEndSkill
